import cv2
import numpy as np
import easyocr

# Initialize the EasyOCR reader
reader = easyocr.Reader(['en'], gpu=False)

def order_points(pts):
    """Orders the four corner points of a rectangle."""
    rect = np.zeros((4, 2), dtype="float32")
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    return rect

def find_and_straighten_card(image_cv):

    try:
        # --- Stage 1: Initial Processing & Resizing ---
        ratio = image_cv.shape[0] / 500.0
        orig = image_cv.copy()
        img_resized = cv2.resize(image_cv, (int(image_cv.shape[1] / ratio), 500))

        # --- Stage 2: Heavy Filtering ---
        gray = cv2.cvtColor(img_resized, cv2.COLOR_BGR2GRAY)
        blurred = cv2.bilateralFilter(gray, 11, 41, 41)

        # --- Stage 3: Threshold + Closing ---
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        thresh = cv2.bitwise_not(thresh)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 15))
        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        # --- Stage 4: Find Contours ---
        contours, _ = cv2.findContours(closed.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"Server log: Contours found: {len(contours)}") # Log to server console
        if not contours:
            return orig # Fallback to original if no contours found

        contours = sorted(contours, key=cv2.contourArea, reverse=True)
        card_contour = contours[0]

        rect = cv2.minAreaRect(card_contour)
        box = cv2.boxPoints(rect)
        box = np.intp(box)

        debug_contour_img = img_resized.copy()
        cv2.drawContours(debug_contour_img, [box], -1, (0, 0, 255), 3)

        # --- Stage 5: Warp Perspective ---
        rect_points = order_points(box.astype("float32") * ratio)
        (tl, tr, br, bl) = rect_points
        widthA = np.linalg.norm(br - bl)
        widthB = np.linalg.norm(tr - tl)
        maxWidth = max(int(widthA), int(widthB))
        heightA = np.linalg.norm(tr - br)
        heightB = np.linalg.norm(tl - bl)
        maxHeight = max(int(heightA), int(heightB))
        dst = np.array([[0, 0], [maxWidth - 1, 0], [maxWidth - 1, maxHeight - 1], [0, maxHeight - 1]], dtype="float32")
        M = cv2.getPerspectiveTransform(rect_points, dst)
        warped = cv2.warpPerspective(orig, M, (maxWidth, maxHeight))

        return warped

    except Exception as e:
        print(f"Server log: An error occurred in find_and_straighten_card: {e}")
        return image_cv 

def get_preprocessed_image_cv(image_bytes: bytes) -> np.ndarray:
    nparr = np.frombuffer(image_bytes, np.uint8)
    img_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    return find_and_straighten_card(img_cv)

def extract_text_from_image(processed_img_cv: np.ndarray) -> str:
    try:
        results = reader.readtext(processed_img_cv, detail=0, paragraph=False)
        return "\n".join(results)
    except Exception as e:
        print(f"Server log: Error during EasyOCR extraction: {e}")
        return ""