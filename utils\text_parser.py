import re
from datetime import datetime

def clean_name_field(text: str):
    if not text:
        return None
    
    # Remove common keywords first
    text = re.sub(r'Name|Father|Holder|Signature', '', text, flags=re.IGNORECASE)
    # Remove any stray non-alphanumeric characters
    cleaned_text = re.sub(r'[^A-Za-z0-9\s]', '', text)
    # Normalize whitespace
    words = cleaned_text.strip().split()
    
    good_words = []
    for word in words:
        if not any(char.isdigit() for char in word):
            good_words.append(word)

    result = ' '.join(good_words)
    
    return result if result else None

def clean_country_field(text: str):

    if not text:
        return None
    
    # Remove keywords and common irrelevant words found near the country name
    text = re.sub(r'Country of Stay|Country|Stay|Gender|M\b|F\b', '', text, flags=re.IGNORECASE)
    # Remove any stray non-alphabetic characters, allowing spaces
    cleaned_text = re.sub(r'[^A-Za-z\s]', '', text)
    # Normalize whitespace and return
    return cleaned_text.strip() if cleaned_text.strip() else None


def parse_cnic_text(raw_text: str) -> dict:
    data = {
        "full_name": None, "father_name": None, "cnic_number": None,
        "date_of_birth": None, "country_of_stay": None, "gender": None, "issue_date": None, "expiry_date": None,
        
    }

    # --- Step 1: High-confidence pattern extraction ---
    text_block = raw_text.replace('\n', ' ')
    
    text_block = re.sub(r'\s{2,}', ' ', text_block)
    
    cnic_pattern = re.compile(r'(\d{5}-\d{7}-\d{1})')
    date_pattern = re.compile(r'(\d{2}[./,-]\d{2}[./,-]\d{4})')

    cnic_match = cnic_pattern.search(text_block)
    if cnic_match: data["cnic_number"] = cnic_match.group(0)

    if re.search(r'\bM\b|Male', text_block, re.IGNORECASE): data["gender"] = "M"
    elif re.search(r'\bF\b|Female', text_block, re.IGNORECASE): data["gender"] = "F"

    date_strings = date_pattern.findall(text_block)
    if date_strings:
        date_objects = []
        for d_str in set(date_strings):
            normalized_d_str = d_str.replace(',', '.').replace('-', '.')
            try: date_objects.append(datetime.strptime(normalized_d_str, '%d.%m.%Y'))
            except ValueError: continue
        sorted_dates = sorted(date_objects)
        if len(sorted_dates) > 0: data["date_of_birth"] = sorted_dates[0].strftime('%d.%m.%Y')
        if len(sorted_dates) > 1: data["issue_date"] = sorted_dates[1].strftime('%d.%m.%Y')
        if len(sorted_dates) > 2: data["expiry_date"] = sorted_dates[2].strftime('%d.%m.%Y')
        elif len(sorted_dates) == 2: data["expiry_date"] = sorted_dates[1].strftime('%d.%m.%Y')

    # --- Step 2: Line-by-Line Parsing for Names and Country of Stay ---
    lines = raw_text.split('\n')
    
    for i, line in enumerate(lines):
        # Helper function for name extraction
        def get_name_value(keyword, cleaner_func):
            value_str = ""
            if len(re.sub(keyword, '', line, flags=re.IGNORECASE).strip()) > 2:
                value_str = re.sub(keyword, '', line, flags=re.IGNORECASE)
            elif i + 1 < len(lines):
                value_str = lines[i+1]
            return cleaner_func(value_str)

        # --- Priority 1: Look for "Father Name" ---
        if re.search(r'Father Name', line, re.IGNORECASE) and not data["father_name"]:
            data["father_name"] = get_name_value(r'Father Name', clean_name_field)
            continue

        # --- Priority 2: Look for generic "Name" ---
        if re.search(r'\bName\b', line, re.IGNORECASE):
            if not data["full_name"]:
                data["full_name"] = get_name_value(r'\bName\b', clean_name_field)
            elif not data["father_name"]:
                data["father_name"] = get_name_value(r'\bName\b', clean_name_field)
        
        # --- Priority 3: Look for "Country" or "Stay" and then find the country name ---
        if re.search(r'\bCountry|Stay\b', line, re.IGNORECASE) and not data["country_of_stay"]:

            search_window_end = min(i + 3, len(lines))
            search_lines = lines[i:search_window_end]

            for search_line in search_lines:
                potential_country = clean_country_field(search_line)
                
                # A plausible country name should be longer than 2 characters
                if potential_country and len(potential_country) > 2:
                    data["country_of_stay"] = potential_country
                    break 
           
            if data["country_of_stay"]:
                continue

    return data