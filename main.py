import base64
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from .utils import ocr_processing
from .utils import text_parser

app = FastAPI(
    title="CNIC OCR"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ImageRequest(BaseModel):
    base64_image: str

@app.post("/cnic", tags=["OCR"])
async def extract_cnic_info(request: ImageRequest):
    try:
        # Decode the base64 string to image bytes
        try:
            image_bytes = base64.b64decode(request.base64_image)
        except Exception as e:
            raise HTTPException(status_code=400, detail="Invalid base64 image data")
        
        processed_img_cv = ocr_processing.get_preprocessed_image_cv(image_bytes)
        raw_text = ocr_processing.extract_text_from_image(processed_img_cv)
        extracted_info = text_parser.parse_cnic_text(raw_text)
        
        # Add the base64 string to the response
        extracted_info['base64_image'] = request.base64_image
        
        return JSONResponse(content=extracted_info)
    except Exception as e:
        print(f"An error occurred in main endpoint: {e}")
        raise HTTPException(status_code=500, detail="Failed to process image.")